mod chat_service;
mod chat_channels;

use dotenv::dotenv;
use std::env;
use tokio::time::{timeout, Duration};
use tokio::sync::oneshot;
use utils::service_discovery::{get_kube_service_endpoints_by_dns, get_service_endpoints_by_dns};
use utils::{health_check, logging};
use chat_service::MyChatService;
use std::collections::HashMap;
use std::net::SocketAddr;
use std::sync::{Arc, Mutex};
use tonic::transport::Server;
use tracing::{info, error, warn};
use crate::chat_service::chat::chat_service_server::ChatServiceServer;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    dotenv().ok();
    let app_name = env!("CARGO_PKG_NAME");
    logging::setup_logging(app_name, &["chat_service", "health_check"]);

    // Set the gRPC server address
    let addr = env::var("LISTEN_ADDR").unwrap_or_else(|_| "0.0.0.0".to_string());
    let port = env::var("SERVICE_PORT").unwrap_or_else(|_| "50055".to_string());

    let clients = Arc::new(Mutex::new(HashMap::new()));

    let chat_service = MyChatService {
        clients: clients.clone(),
        local_channel: Arc::new(chat_channels::local_chat::LocalChat),
        shout_channel: Arc::new(chat_channels::shout_chat::ShoutChat),
        guild_channel: Arc::new(chat_channels::guild_chat::GuildChat),
    };

    // Register service with Consul
    let (mut health_reporter, health_service) = tonic_health::server::health_reporter();
    health_reporter
        .set_serving::<ChatServiceServer<MyChatService>>()
        .await;
    let address = SocketAddr::new(addr.parse()?, port.parse()?);
    tokio::spawn(
        Server::builder()
            .add_service(chat_service.into_service())
            .add_service(health_service)
            .serve(address),
    );

    println!("Chat Service listening on {}", address);
    
    utils::signal_handler::wait_for_signal().await;
    Ok(())
}
