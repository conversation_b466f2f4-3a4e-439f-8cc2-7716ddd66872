use std::env;
use kube::{
    api::{Api, <PERSON><PERSON><PERSON><PERSON>, DeletePara<PERSON>},
    Client,
};
use k8s_openapi::api::core::v1::{Pod, Service};
use serde_json::json;
use std::error::Error;
use tokio::time::{sleep, Duration, Instant};

/// Struct representing connection info for a game-logic instance.
#[derive(Debug)]
pub struct ConnectionInfo {
    pub ip: String,
    pub port: u16,
}

/// The `K8sOrchestrator` struct wraps a Kubernetes client and the
/// namespace where your game logic instances will be created.
pub struct K8sOrchestrator {
    client: Client,
    namespace: String,
}

fn get_log_level() -> String {
    env::var("LOGIC_LOG_LEVEL").unwrap_or_else(|_| env::var("LOG_LEVEL").unwrap_or_else(|_| "info".to_string()))
}

impl K8sOrchestrator {
    /// Creates a new orchestrator for the given namespace.
    pub async fn new(namespace: &str) -> Result<Self, Box<dyn Error>> {
        let client = Client::try_default().await?;
        Ok(Self {
            client,
            namespace: namespace.to_string(),
        })
    }

    /// Creates a new game-logic Pod with the given `instance_name` and container `image`.
    /// Adjust the pod manifest as needed for your game-logic container.
    pub async fn create_game_logic_instance(
        &self,
        instance_name: &str,
        image: &str,
        map_id: u32,
    ) -> Result<Pod, Box<dyn Error>> {
        let pods: Api<Pod> = Api::namespaced(self.client.clone(), &self.namespace);

        // Define the pod manifest for the new game-logic instance.
        let map_id_str = map_id.to_string();
        let pod_manifest = json!({
            "apiVersion": "v1",
            "kind": "Pod",
            "metadata": {
                "name": instance_name,
                "labels": {
                    "app": "game-logic",
                    "map_id": map_id_str
                }
            },
            "spec": {
                "containers": [{
                    "name": "game-logic",
                    "image": image,
                    "ports": [{
                        "containerPort": 50056,
                        "name": "grpc"
                    }],
                    "env": [
                        {
                            "name": "MAP_ID",
                            "value": map_id_str
                        },
                        {
                            "name": "LOG_LEVEL",
                            "value": get_log_level()
                        }
                    ],
                    "volumeMounts": [{
                        "name": "game-data",
                        "mountPath": "/opt/data",
                        "readOnly": true
                    }]
                }],
                "volumes": [{
                    "name": "game-data",
                    "persistentVolumeClaim": {
                        "claimName": "game-data-pvc"
                    }
                }]
            }
        });

        // Deserialize the JSON manifest into a Pod struct.
        let pod: Pod = serde_json::from_value(pod_manifest)?;

        // Create the Pod in Kubernetes.
        let created_pod = pods.create(&PostParams::default(), &pod).await?;
        Ok(created_pod)
    }

    /// Retrieves the updated Pod object for a given instance name.
    pub async fn get_instance(&self, instance_name: &str)
                              -> Result<Pod, Box<dyn Error>> {
        let pods: Api<Pod> = Api::namespaced(self.client.clone(), &self.namespace);
        let pod = pods.get(instance_name).await?;
        Ok(pod)
    }

    /// Checks the status of the game-logic Pod and returns its gRPC connection info.
    /// It attempts to determine the port from the pod's container spec (searching for a port
    /// named "grpc"). If not found, it falls back to the default port 50051.
    pub async fn get_connection_info(&self, instance_name: &str)
                                     -> Result<Option<ConnectionInfo>, Box<dyn Error>>
    {
        let pod = self.get_instance(instance_name).await?;
        if let Some(status) = pod.status {
            if let Some(pod_ip) = status.pod_ip {
                // Try to extract the container port dynamically.
                if let Some(spec) = pod.spec {
                    if let Some(container) = spec.containers.first() {
                        if let Some(ports) = &container.ports {
                            // Look for a port with the name "grpc"
                            if let Some(grpc_port) = ports.iter().find(|p| {
                                p.name.as_ref().map_or(false, |n| n == "grpc")
                            }) {
                                return Ok(Some(ConnectionInfo {
                                    ip: pod_ip,
                                    port: grpc_port.container_port as u16,
                                }));
                            }
                            // Or use the first container port if no named port was found.
                            if let Some(first_port) = ports.first() {
                                return Ok(Some(ConnectionInfo {
                                    ip: pod_ip,
                                    port: first_port.container_port as u16,
                                }));
                            }
                        }
                    }
                }
                // Use fallback port if no port information is available.
                return Ok(Some(ConnectionInfo { ip: pod_ip, port: 50051 }));
            }
        }
        Ok(None)
    }

    /// Polls for connection info until successful or a timeout is reached.
    /// `timeout_secs` specifies the maximum time in seconds to wait.
    pub async fn poll_connection_info(
        &self,
        instance_name: &str,
        timeout_secs: u64,
    ) -> Result<ConnectionInfo, Box<dyn Error>> {
        let start = Instant::now();
        let timeout = Duration::from_secs(timeout_secs);
        // Poll every 2 seconds
        let poll_interval = Duration::from_secs(2);

        loop {
            if start.elapsed() > timeout {
                return Err(format!(
                    "Timeout reached while polling connection info for pod {}",
                    instance_name
                )
                    .into());
            }

            if let Some(conn_info) = self.get_connection_info(instance_name).await? {
                return Ok(conn_info);
            }
            sleep(poll_interval).await;
        }
    }

    /// Shuts down (deletes) the game-logic Pod with the given name.
    pub async fn shutdown_instance(&self, instance_name: &str)
                                   -> Result<(), Box<dyn Error>> {
        let pods: Api<Pod> = Api::namespaced(self.client.clone(), &self.namespace);
        // DeleteParams::default() is sufficient for a forceful deletion.
        pods.delete(instance_name, &DeleteParams::default()).await?;
        Ok(())
    }
}