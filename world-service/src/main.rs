mod k8s_orchestrator;
mod world_service;
mod game_logic_client;

use dotenv::dotenv;
use std::env;
use std::sync::Arc;
use tokio::time::{sleep, Duration, timeout};
use tokio::sync::oneshot;
use tonic::transport::Server;
use utils::service_discovery::{get_kube_service_endpoints_by_dns, get_service_endpoints_by_dns};
use utils::{health_check, logging};
use tracing::{debug, error, info, warn};
use crate::k8s_orchestrator::K8sOrchestrator;
use crate::world_service::{MyWorldService, MyWorldGameLogicService};
use crate::game_logic_client::GameLogicClientManager;
use world_service::world::world_service_server::WorldServiceServer;
use world_service::world::world_game_logic_service_server::WorldGameLogicServiceServer;

fn get_service_name() -> String {
    env::var("WORLD_SERVICE_NAME").unwrap_or_else(|_| "default-service".to_string())
}

fn get_map_ids() -> Vec<u32> {
    // Get the `MAP_IDS` environment variable, such as "42,43,44,45"
    let map_ids_str = env::var("MAP_IDS").unwrap_or_default();
    // Split the string by commas and parse each into a u32. Ignore invalid entries.
    map_ids_str
        .split(',')
        .filter_map(|s| s.trim().parse::<u32>().ok())
        .collect()
}

/// Get connection retry configuration from environment variables with sensible defaults
fn get_connection_retry_config() -> (u32, Duration, Duration) {
    let max_retries = env::var("CONNECTION_INFO_MAX_RETRIES")
        .unwrap_or_else(|_| "3".to_string())
        .parse::<u32>()
        .unwrap_or(3);

    let initial_delay_ms = env::var("CONNECTION_INFO_INITIAL_DELAY_MS")
        .unwrap_or_else(|_| "2000".to_string())
        .parse::<u64>()
        .unwrap_or(2000);

    let max_delay_ms = env::var("CONNECTION_INFO_MAX_DELAY_MS")
        .unwrap_or_else(|_| "10000".to_string())
        .parse::<u64>()
        .unwrap_or(10000);

    (
        max_retries,
        Duration::from_millis(initial_delay_ms),
        Duration::from_millis(max_delay_ms),
    )
}

/// Retry wrapper for getting connection info with exponential backoff
async fn get_connection_info_with_retry(
    orchestrator: &K8sOrchestrator,
    instance_name: &str,
    poll_timeout_secs: u64,
    max_retries: u32,
    initial_delay: Duration,
    max_delay: Duration,
) -> Result<crate::k8s_orchestrator::ConnectionInfo, Box<dyn std::error::Error>> {
    let mut delay = initial_delay;
    let mut last_error = None;

    for attempt in 0..=max_retries {
        match orchestrator.poll_connection_info(instance_name, poll_timeout_secs).await {
            Ok(conn_info) => {
                if attempt > 0 {
                    info!("Successfully retrieved connection info for {} instance after {} attempts", instance_name, attempt + 1);
                }
                return Ok(conn_info);
            }
            Err(e) => {
                last_error = Some(e);
                if attempt < max_retries {
                    warn!("Failed to retrieve connection info for {} instance (attempt {}): {}. Retrying in {:?}...",
                          instance_name, attempt + 1, last_error.as_ref().unwrap(), delay);
                    sleep(delay).await;
                    delay = std::cmp::min(delay * 2, max_delay);
                } else {
                    error!("Failed to retrieve connection info for {} instance after {} attempts: {}",
                           instance_name, max_retries + 1, last_error.as_ref().unwrap());
                    return Err(last_error.unwrap());
                }
            }
        }
    }

    // This should never be reached due to the loop logic above
    unreachable!()
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    dotenv().ok();
    let app_name = env!("CARGO_PKG_NAME");
    logging::setup_logging(app_name, &["world_service", "health_check"]);
    
    // Get the list of map IDs from the environment variable
    let map_ids = get_map_ids();
    
    // Get the service name from the environment variable
    let service_name = get_service_name();
    
    let instance_names = map_ids.iter().map(|map_id| format!("world-{}-{}", service_name, map_id).to_lowercase()).collect::<Vec<_>>();
    
    // Create a game-logic instance for each map ID we want to manage
    let orchestrator = K8sOrchestrator::new("default").await?;
    let image = "gitea.azgstudio.com/raven/game-logic-service:latest";
    for (map_id, instance_name) in map_ids.iter().zip(instance_names.iter()) {
        match orchestrator.create_game_logic_instance(&instance_name, image, *map_id).await
        {
            Ok(created_pod) => {
                debug!(
                    "Successfully created game-logic instance: {:?}",
                    created_pod.metadata.name,
                );
            }
            Err(e) => {
                if e.to_string().contains("AlreadyExists") {
                    warn!("Game-logic instance already exists: {}", e);
                    // No reason to return an error here.
                    //TODO: We may want to check to make sure the pod is working correctly.
                } else {
                    error!("Error creating game-logic instance: {}", e);
                    return Err(e);
                }
            }
        }
    }
    
    // Create game logic client manager and world service
    let game_logic_manager = Arc::new(GameLogicClientManager::new());
    let world_service_shared = Arc::new(MyWorldService::new());
    let world_service = MyWorldService::new();
    let world_game_logic_service = MyWorldGameLogicService::new(world_service_shared.clone());

    // Get retry configuration for connection info
    let (conn_max_retries, conn_initial_delay, conn_max_delay) = get_connection_retry_config();

    // Connect to game logic instances
    for (map_id, instance_name) in map_ids.iter().zip(instance_names.clone()) {
        match get_connection_info_with_retry(
            &orchestrator,
            &instance_name,
            30, // poll timeout in seconds (existing behavior)
            conn_max_retries,
            conn_initial_delay,
            conn_max_delay
        ).await {
            Ok(conn_info) => {
                debug!("Successfully retrieved connection info for {} instance: {:?}", instance_name, conn_info);
                let endpoint = format!("http://{}:{}", conn_info.ip, conn_info.port);

                // Add game logic client with retry logic
                match game_logic_manager.add_client(*map_id, endpoint).await {
                    Ok(()) => {
                        info!("Successfully connected to game logic service for map {}", map_id);
                    }
                    Err(e) => {
                        error!("Failed to add game logic client for map {} after retries: {}", map_id, e);
                        // Continue with other instances instead of failing completely
                        // This allows the service to start even if some game logic instances are not ready
                    }
                }
            }
            Err(e) => {
                error!("Error retrieving connection info for {} instance after retries: {}", instance_name, e);
                // Continue with other instances instead of failing completely
                // This allows the service to start even if some pods are having issues
                warn!("Skipping map {} due to connection info retrieval failure", map_id);
            }
        }
    }

    // Set the gRPC server address
    let addr = env::var("LISTEN_ADDR").unwrap_or_else(|_| "0.0.0.0".to_string());
    let port = env::var("SERVICE_PORT").unwrap_or_else(|_| "50054".to_string());
    // let db_url = format!(
    //     "http://{}",
    //     get_kube_service_endpoints_by_dns("database-service", "tcp", "database-service")
    //         .await?
    //         .get(0)
    //         .unwrap()
    // );
    // let chat_service = format!(
    //     "http://{}",
    //     get_kube_service_endpoints_by_dns("chat-service", "tcp", "chat-service")
    //         .await?
    //         .get(0)
    //         .unwrap()
    // );

    // Start gRPC server
    let grpc_addr = format!("{}:{}", addr, port).parse()?;
    info!("Starting World Service gRPC server on {}", grpc_addr);

    let server_task = tokio::spawn(async move {
        Server::builder()
            .add_service(WorldServiceServer::new(world_service))
            .add_service(WorldGameLogicServiceServer::new(world_game_logic_service))
            .serve(grpc_addr)
            .await
    });

    // Register service with Consul
    health_check::start_health_check(addr.as_str()).await?;

    // Wait for shutdown signal
    utils::signal_handler::wait_for_signal().await;

    // Shutdown server
    server_task.abort();
    
    // Shutdown all game-logic instances
    let instances: Vec<_> = instance_names.iter().map(|instance_name| orchestrator.shutdown_instance(instance_name)).collect();
    for instance in instances {
        instance.await?;
    }
    Ok(())
}
